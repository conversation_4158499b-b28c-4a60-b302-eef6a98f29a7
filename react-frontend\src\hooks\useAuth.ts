import { useState, useEffect } from 'react';
import { User } from '@/types';

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for existing session on mount
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      setIsLoading(true);
      
      // Check localStorage for user session
      const savedUser = localStorage.getItem('user');
      if (savedUser) {
        const userData = JSON.parse(savedUser);
        setUser(userData);
        
        // Update body classes for logged-in state exactly like original
        document.body.classList.remove('not-logged-in');
        document.body.classList.add('logged-in');
        document.documentElement.classList.remove('not-logged-in');
        document.documentElement.classList.add('logged-in');
      } else {
        // Set not-logged-in state exactly like original
        document.body.classList.add('not-logged-in');
        document.body.classList.remove('logged-in');
        document.documentElement.classList.add('not-logged-in');
        document.documentElement.classList.remove('logged-in');
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    try {
      // Simulate API call - replace with actual authentication
      const userData: User = {
        id: '1',
        email,
        fullName: 'User Name',
        employeeId: 'EMP001',
        isLoggedIn: true,
      };

      setUser(userData);
      localStorage.setItem('user', JSON.stringify(userData));
      
      // Update body classes
      document.body.classList.remove('not-logged-in');
      document.body.classList.add('logged-in');
      document.documentElement.classList.remove('not-logged-in');
      document.documentElement.classList.add('logged-in');

      return { success: true };
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: 'Login failed' };
    }
  };

  const register = async (fullName: string, email: string, password: string, employeeId?: string) => {
    try {
      // Simulate API call - replace with actual registration
      const userData: User = {
        id: '1',
        email,
        fullName,
        employeeId,
        isLoggedIn: true,
      };

      setUser(userData);
      localStorage.setItem('user', JSON.stringify(userData));
      
      // Update body classes
      document.body.classList.remove('not-logged-in');
      document.body.classList.add('logged-in');
      document.documentElement.classList.remove('not-logged-in');
      document.documentElement.classList.add('logged-in');

      return { success: true };
    } catch (error) {
      console.error('Registration error:', error);
      return { success: false, error: 'Registration failed' };
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('user');
    
    // Update body classes
    document.body.classList.add('not-logged-in');
    document.body.classList.remove('logged-in');
    document.documentElement.classList.add('not-logged-in');
    document.documentElement.classList.remove('logged-in');
  };

  const updateUser = (updates: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...updates };
      setUser(updatedUser);
      localStorage.setItem('user', JSON.stringify(updatedUser));
    }
  };

  return {
    user,
    isLoading,
    isLoggedIn: !!user,
    login,
    register,
    logout,
    updateUser,
    checkAuthStatus,
  };
};
