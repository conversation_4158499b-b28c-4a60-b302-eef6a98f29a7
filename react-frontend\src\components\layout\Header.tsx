import React, { useState } from 'react';
import { User } from '@/types';

interface HeaderProps {
  user: User | null;
  sidebarCollapsed: boolean;
  onToggleSidebar: () => void;
  onNewChat: () => void;
  onOpenSettings: () => void;
  onLogout: () => void;
}

const Header: React.FC<HeaderProps> = ({
  user,
  sidebarCollapsed,
  onToggleSidebar,
  onNewChat,
  onOpenSettings,
  onLogout,
}) => {
  const [showUserDropdown, setShowUserDropdown] = useState(false);

  const handleUserMenuClick = () => {
    setShowUserDropdown(!showUserDropdown);
  };

  const handleSettingsClick = () => {
    setShowUserDropdown(false);
    onOpenSettings();
  };

  const handleLogoutClick = () => {
    setShowUserDropdown(false);
    onLogout();
  };

  return (
    <header className="main-header" id="mainHeader">
      <div className="header-content">
        {/* Left section */}
        <div className="header-left">
          {sidebarCollapsed && (
            <button
              className="header-new-chat-btn"
              onClick={onNewChat}
              title="New chat"
            >
              <img src="/new-chat-icon-larger.svg" alt="New Chat" style={{ width: '20px', height: '20px' }} />
            </button>
          )}
          <div className="header-brand">
            <h1>ZiaHR</h1>
          </div>
        </div>

        {/* Right section */}
        <div className="header-right">
          <div className="user-account-dropdown">
            <button
              className="user-account-btn"
              id="userAccountBtn"
              onClick={handleUserMenuClick}
              title="Account"
            >
              <i className="fas fa-user-circle"></i>
            </button>

            {showUserDropdown && (
              <div className="user-dropdown-menu" id="userDropdownMenu">
                <div className="user-info">
                  <div className="user-name">{user?.fullName || 'User'}</div>
                  <div className="user-email">{user?.email}</div>
                  {user?.employeeId && (
                    <div className="user-id">ID: {user.employeeId}</div>
                  )}
                </div>
                <div className="dropdown-divider"></div>
                <div className="dropdown-menu-items">
                  <button className="dropdown-item" onClick={handleSettingsClick}>
                    <i className="fas fa-cog"></i>
                    <span>Settings</span>
                  </button>
                  <button className="dropdown-item" onClick={handleLogoutClick}>
                    <i className="fas fa-sign-out-alt"></i>
                    <span>Log out</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Click outside to close dropdown */}
      {showUserDropdown && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowUserDropdown(false)}
        />
      )}
    </header>
  );
};

export default Header;
