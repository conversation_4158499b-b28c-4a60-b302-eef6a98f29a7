import React from 'react';
import { Message } from '@/types';

interface MessageBubbleProps {
  message: Message;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({ message }) => {
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const renderMessageContent = (content: string) => {
    // Simple markdown-like rendering for basic formatting
    const lines = content.split('\n');
    return lines.map((line, index) => (
      <div key={index}>
        {line}
        {index < lines.length - 1 && <br />}
      </div>
    ));
  };

  const renderFileAttachments = () => {
    if (!message.files || message.files.length === 0) return null;

    return (
      <div className="message-files">
        {message.files.map((file) => (
          <div key={file.id} className="message-file">
            <i className="fas fa-file"></i>
            <span className="message-file-name">{file.name}</span>
            <span className="message-file-size">{(file.size / 1024).toFixed(1)} KB</span>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className={`message-container ${message.isUser ? 'user-message' : 'bot-message'}`}>
      <div className={`message-bubble ${message.isUser ? 'message-user' : 'message-bot'}`}>
        {/* Message Content */}
        <div className="message-content">
          {renderMessageContent(message.content)}
        </div>

        {/* File Attachments */}
        {renderFileAttachments()}

        {/* Timestamp */}
        <div className="message-timestamp">
          {formatTime(message.timestamp)}
        </div>
      </div>
    </div>
  );
};

export default MessageBubble;
