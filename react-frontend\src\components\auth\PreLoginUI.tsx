import React, { useState } from 'react';
import SuggestionChips from '@/components/chat/SuggestionChips';

interface PreLoginUIProps {
  onLogin: () => void;
  onSendMessage: (message: string) => void;
}

const PreLoginUI: React.FC<PreLoginUIProps> = ({ onLogin, onSendMessage }) => {
  const [inputValue, setInputValue] = useState('');
  const [showSendButton, setShowSendButton] = useState(false);

  const suggestions = [
    { text: "🗓️ Leave Policy", query: "What is the company's leave policy?" },
    { text: "👥 Referral Program", query: "How does the employee referral program work?" },
    { text: "👔 Dress Code", query: "What is the dress code policy?" },
    { text: "🏠 Work from Home", query: "Tell me about the work from home policy" },
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setInputValue(value);
    setShowSendButton(value.trim().length > 0);
    
    // Auto-resize textarea
    e.target.style.height = 'auto';
    e.target.style.height = e.target.scrollHeight + 'px';
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputValue.trim()) {
      onSendMessage(inputValue.trim());
      setInputValue('');
      setShowSendButton(false);
    }
  };

  const handleSuggestionClick = (query: string) => {
    onSendMessage(query);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  return (
    <div className="pre-login-container" role="main" aria-label="Pre-login chat interface">
      {/* Header */}
      <header className="pre-login-header" role="banner">
        <div className="pre-login-logo" aria-label="ZiaHR logo and brand">
          <i className="fas fa-comment-dots" aria-hidden="true"></i>
          <span>ZiaHR</span>
        </div>
        <div className="pre-login-actions">
          <button
            className="login-btn"
            id="preLoginBtn"
            aria-label="Log in to your account"
            onClick={onLogin}
          >
            Log in
          </button>
        </div>
      </header>

      {/* Main Content */}
      <main className="pre-login-main" aria-labelledby="prelogin-welcome-heading">
        {/* Welcome message container */}
        <div className="pre-login-welcome-container" role="region" aria-label="Welcome message and suggestions">
          <div className="pre-login-welcome-message">
            <h2 id="prelogin-welcome-heading">👋 Welcome to ZiaHR</h2>
            <p>I can help you with questions about company policies, employee guidelines, and HR procedures.</p>
            <div className="suggestion-chips" style={{ display: 'flex', flexDirection: 'row', justifyContent: 'center', alignItems: 'center', gap: '16px' }}>
              {suggestions.map((suggestion, index) => (
                <button
                  key={index}
                  className="suggestion-chip"
                  onClick={() => handleSuggestionClick(suggestion.query)}
                >
                  {suggestion.text}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Input container at the bottom - ChatGPT Style */}
        <div className="pre-login-bottom-input-container" role="region" aria-label="Message input area">
          <form className="pre-login-chat-input-form" onSubmit={handleSubmit}>
            <div className="chatgpt-input-wrapper">
              {/* Main textarea at top */}
              <div className="chatgpt-input-area">
                <label htmlFor="preLoginChatInput" style={{ position: 'absolute', left: '-9999px', top: 'auto', width: '1px', height: '1px', overflow: 'hidden' }}>
                  Type your question here
                </label>
                <textarea
                  id="preLoginChatInput"
                  className="pre-login-input"
                  placeholder="How can I help you today?"
                  rows={1}
                  aria-label="Type your question here"
                  aria-multiline="true"
                  value={inputValue}
                  onChange={handleInputChange}
                  onKeyDown={handleKeyDown}
                  style={{ minHeight: '24px', maxHeight: '200px' }}
                />
              </div>

              {/* Tools row below textarea */}
              <div className="chatgpt-tools-row">
                <div className="chatgpt-left-tools">
                  {/* File upload */}
                  <button type="button" className="chatgpt-tool-btn pre-login-action-btn" aria-label="Attach a file">
                    <i className="fas fa-paperclip"></i>
                  </button>
                  {/* Voice input */}
                  <button type="button" className="chatgpt-tool-btn pre-login-action-btn" aria-label="Start voice input">
                    <i className="fas fa-microphone"></i>
                  </button>
                </div>

                <div className="chatgpt-right-tools">
                  {/* Send button (appears when typing) */}
                  {showSendButton && (
                    <button
                      type="submit"
                      className="chatgpt-send-btn pre-login-send-btn"
                      title="Send message"
                      aria-label="Send message"
                    >
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M7 11L12 6L17 11M12 18V7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </button>
                  )}
                </div>
              </div>
            </div>
          </form>
        </div>
      </main>
    </div>
  );
};

export default PreLoginUI;
