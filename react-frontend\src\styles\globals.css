@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Custom Properties - Exact match to original */
:root {
  /* Light Theme (Default) - <PERSON>, <PERSON>, Gray Theme */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F8F8F8;
  --text-primary: #000000;
  --text-secondary: #666666;
  --accent-color: #333333;
  --accent-color-rgb: 51, 51, 51;
  --accent-hover: #555555;
  --border-color: #DADADA;
  --message-user-bg: #F2F2F2;
  --message-bot-bg: #FFFFFF;
  --message-text: #000000;
  --shadow-color: rgba(0, 0, 0, 0.06);
  --input-bg: #FFFFFF;
  --input-border: #DADADA;
  --input-text: #000000;
  --input-placeholder: #999999;
  --welcome-bg: #FFFFFF;
  --chip-bg: #F2F2F2;
  --chip-text: #000000;
  --chip-hover-bg: #333333;
  --chip-hover-text: #FFFFFF;
  --button-disabled: #E5E5E5;
  --cta-color: #333333;
  --cta-hover: #555555;
  --success-color: #444444;
  --error-color: #666666;
  --header-height: 60px;
  --footer-height: 100px;
  --resizer-width: 5px;
  --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
  --chatgpt-message-spacing: 30px;
  --chatgpt-message-padding: 16px;
}

/* Dark Theme */
.theme-dark {
  --bg-primary: #1E1E1E;
  --bg-secondary: #2B2B2B;
  --text-primary: #FFFFFF;
  --text-secondary: #EDEDED;
  --accent-color: #999999;
  --accent-color-rgb: 153, 153, 153;
  --accent-hover: #AAAAAA;
  --border-color: #333333;
  --message-user-bg: rgba(255, 255, 255, 0.1);
  --message-bot-bg: rgba(255, 255, 255, 0.05);
  --message-text: #FFFFFF;
  --shadow-color: rgba(0, 0, 0, 0.3);
  --sidebar-bg: #2c2c2c;
  --sidebar-text: #e5e5e5;
  --sidebar-hover: #3a3a3a;
  --sidebar-border: #404040;
  --sidebar-item-hover: rgba(255, 255, 255, 0.1);
  --sidebar-item-active: #404040;
  --sidebar-accent: #e5e5e5;
  --input-bg: #343541;
  --input-border: #444654;
  --input-text: #FFFFFF;
  --input-placeholder: #BBBBBB;
  --welcome-bg: #1E1E1E;
  --chip-bg: rgba(255, 255, 255, 0.1);
  --chip-text: #FFFFFF;
  --chip-hover-bg: #666666;
  --chip-hover-text: #FFFFFF;
  --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  --cta-color: #999999;
  --cta-hover: #AAAAAA;
  --success-color: #AAAAAA;
  --error-color: #777777;
}

/* Global styles and CSS custom properties */
@layer base {
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
  }

  html {
    height: 100%;
  }

  body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color 0.2s ease, color 0.2s ease;
    height: 100vh;
    overflow: hidden;
    font-size: 1rem;
    line-height: 1.5;
    font-weight: 400;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Typography styles */
  h1, h2, h3, h4, h5, h6 {
    font-weight: 700; /* Bold weight for headings */
  }

  p, li, span, div {
    font-weight: 400; /* Regular weight for body text */
  }

  /* Remove any unwanted red outlines or borders */
  .chat-messages *,
  .welcome-container *,
  .chat-input-container * {
    outline: none !important;
    border-color: var(--border-color) !important;
  }

  /* Scrollbar styles */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-primary-border rounded;
  }

  .dark ::-webkit-scrollbar-thumb {
    @apply bg-dark-border;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-primary-accent;
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    @apply bg-dark-accent;
  }
}

@layer components {
  /* Pre-login UI Components */
  .pre-login-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: var(--bg-primary);
    color: var(--text-primary);
  }

  .pre-login-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-primary);
  }

  .pre-login-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
  }

  .pre-login-logo i {
    font-size: 24px;
    color: var(--accent-color);
  }

  .login-btn {
    padding: 8px 16px;
    background-color: var(--cta-color);
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .login-btn:hover {
    background-color: var(--cta-hover);
  }

  .pre-login-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 32px 16px;
    position: relative;
  }

  .pre-login-welcome-container {
    text-align: center;
    max-width: 800px;
    margin-bottom: 48px;
  }

  .pre-login-welcome-message h2 {
    font-size: 32px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 16px;
  }

  .pre-login-welcome-message p {
    font-size: 18px;
    color: var(--text-secondary);
    margin-bottom: 32px;
    line-height: 1.6;
  }

  .suggestion-chips {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 16px;
  }

  .suggestion-chip {
    padding: 12px 20px;
    background-color: var(--chip-bg);
    color: var(--chip-text);
    border: none;
    border-radius: 24px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
  }

  .suggestion-chip:hover {
    background-color: var(--chip-hover-bg);
    color: var(--chip-hover-text);
    transform: translateY(-1px);
  }

  .pre-login-bottom-input-container {
    position: absolute;
    bottom: 32px;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 768px;
    padding: 0 16px;
  }

  .chatgpt-input-wrapper {
    background-color: var(--input-bg);
    border: 1px solid var(--input-border);
    border-radius: 12px;
    box-shadow: var(--card-shadow);
    overflow: hidden;
  }

  .chatgpt-input-area {
    padding: 16px;
  }

  .pre-login-input {
    width: 100%;
    background: transparent;
    border: none;
    outline: none;
    resize: none;
    font-size: 16px;
    line-height: 1.5;
    color: var(--input-text);
    font-family: inherit;
  }

  .pre-login-input::placeholder {
    color: var(--input-placeholder);
  }

  .chatgpt-tools-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px 16px;
  }

  .chatgpt-left-tools,
  .chatgpt-right-tools {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .chatgpt-tool-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: transparent;
    border: none;
    border-radius: 6px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .chatgpt-tool-btn:hover {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
  }

  .chatgpt-send-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background-color: var(--cta-color);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .chatgpt-send-btn:hover {
    background-color: var(--cta-hover);
  }

  .chatgpt-send-btn:disabled {
    background-color: var(--button-disabled);
    cursor: not-allowed;
  }

  /* App Container */
  .app-container {
    display: flex;
    height: 100vh;
    background-color: var(--bg-primary);
    color: var(--text-primary);
  }

  /* Sidebar Styles */
  .sidebar {
    width: 280px;
    background-color: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    transition: width 0.3s ease;
    overflow: hidden;
  }

  .sidebar.collapsed {
    width: 60px;
  }

  .sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
    min-height: 60px;
  }

  .sidebar-brand-logo {
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: opacity 0.3s ease;
  }

  .sidebar.collapsed .sidebar-brand-logo {
    opacity: 0;
    pointer-events: none;
  }

  .sidebar-app-icon {
    width: 24px;
    height: 24px;
  }

  .sidebar-toggle {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
  }

  .sidebar-toggle:hover {
    background-color: var(--bg-primary);
    color: var(--text-primary);
  }

  .sidebar-nav {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
  }

  .sidebar-menu {
    padding: 16px 8px;
  }

  .sidebar-menu-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    margin-bottom: 4px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-primary);
  }

  .sidebar-menu-item:hover {
    background-color: var(--bg-primary);
  }

  .sidebar-menu-icon {
    width: 20px;
    height: 20px;
    margin-right: 12px;
    color: var(--text-secondary);
  }

  .sidebar-menu-text {
    font-size: 14px;
    font-weight: 500;
    transition: opacity 0.3s ease;
  }

  .sidebar.collapsed .sidebar-menu-text {
    opacity: 0;
    pointer-events: none;
  }

  .sidebar.collapsed .sidebar-menu-item {
    justify-content: center;
    padding: 12px 8px;
  }

  .sidebar.collapsed .sidebar-menu-icon {
    margin-right: 0;
  }

  .sidebar-conversations {
    flex: 1;
    padding: 0 8px;
    overflow-y: auto;
  }

  .sidebar-section-header {
    padding: 8px 16px;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 8px;
  }

  .sidebar-section-title {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .chat-history-list {
    max-height: 400px;
    overflow-y: auto;
  }

  .chat-history-item {
    position: relative;
    padding: 12px 16px;
    margin-bottom: 4px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .chat-history-item:hover {
    background-color: var(--bg-primary);
  }

  .chat-history-item.active {
    background-color: var(--bg-primary);
    border-left: 3px solid var(--accent-color);
  }

  .chat-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 4px;
    line-height: 1.3;
  }

  .chat-date {
    font-size: 12px;
    color: var(--text-secondary);
  }

  .chat-actions {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .chat-history-item:hover .chat-actions {
    opacity: 1;
  }

  .chat-actions button {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  .chat-actions button:hover {
    background-color: var(--border-color);
    color: var(--text-primary);
  }

  .sidebar-bottom {
    padding: 16px;
    border-top: 1px solid var(--border-color);
  }

  .upload-form {
    margin-bottom: 8px;
  }

  .upload-status {
    font-size: 12px;
    color: var(--text-secondary);
  }

  /* Hide sidebar content when collapsed */
  .sidebar.collapsed .sidebar-conversations,
  .sidebar.collapsed .sidebar-bottom {
    display: none;
  }

  /* Header Styles */
  .main-header {
    height: var(--header-height);
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    z-index: 100;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 0 24px;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .header-new-chat-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
  }

  .header-new-chat-btn:hover {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
  }

  .header-brand h1 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
  }

  .header-right {
    display: flex;
    align-items: center;
  }

  .user-account-dropdown {
    position: relative;
  }

  .user-account-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    font-size: 20px;
    transition: all 0.2s ease;
  }

  .user-account-btn:hover {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
  }

  .user-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 8px;
    width: 280px;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: var(--card-shadow);
    z-index: 1000;
    animation: slideIn 0.2s ease-out;
  }

  .user-info {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
  }

  .user-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
  }

  .user-email {
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 2px;
  }

  .user-id {
    font-size: 12px;
    color: var(--text-secondary);
  }

  .dropdown-divider {
    height: 1px;
    background-color: var(--border-color);
  }

  .dropdown-menu-items {
    padding: 8px 0;
  }

  .dropdown-item {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 12px 16px;
    background: transparent;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    transition: background-color 0.2s ease;
    text-align: left;
  }

  .dropdown-item:hover {
    background-color: var(--bg-secondary);
  }

  .dropdown-item i {
    margin-right: 12px;
    width: 16px;
    color: var(--text-secondary);
  }

  .dropdown-item span {
    font-size: 14px;
  }

  /* Main Content Styles */
  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: var(--bg-primary);
    overflow: hidden;
  }

  .chat-messages-container {
    flex: 1;
    overflow-y: auto;
    position: relative;
  }

  .welcome-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    padding: 32px 16px;
    text-align: center;
  }

  .welcome-message h2 {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 16px;
  }

  .welcome-message p {
    font-size: 16px;
    color: var(--text-secondary);
    margin-bottom: 32px;
    line-height: 1.6;
    max-width: 600px;
  }

  .chat-messages {
    padding: var(--chatgpt-message-spacing);
  }

  .chat-input-container {
    border-top: 1px solid var(--border-color);
    background-color: var(--bg-primary);
    padding: 16px 24px;
  }

  .chat-input-form {
    width: 100%;
    max-width: 768px;
    margin: 0 auto;
  }

  .chat-input {
    width: 100%;
    background: transparent;
    border: none;
    outline: none;
    resize: none;
    font-size: 16px;
    line-height: 1.5;
    color: var(--input-text);
    font-family: inherit;
  }

  .chat-input::placeholder {
    color: var(--input-placeholder);
  }

  .escalation-btn {
    color: #dc2626 !important;
  }

  .escalation-btn:hover {
    background-color: rgba(220, 38, 38, 0.1) !important;
  }

  .logged-in-suggestions {
    margin-top: 16px;
    display: flex;
    justify-content: center;
  }

  /* Message Styles */
  .message-container {
    margin-bottom: var(--chatgpt-message-spacing);
  }

  .message-bubble {
    max-width: 70%;
    padding: var(--chatgpt-message-padding);
    border-radius: 12px;
    line-height: 1.5;
  }

  .message-user {
    margin-left: auto;
    background-color: var(--message-user-bg);
    color: var(--message-text);
  }

  .message-bot {
    margin-right: auto;
    background-color: var(--message-bot-bg);
    color: var(--message-text);
    border: 1px solid var(--border-color);
  }

  .message-timestamp {
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: 8px;
  }

  .message-files {
    margin-top: 12px;
  }

  .message-file {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 8px;
  }

  .message-file i {
    margin-right: 8px;
    color: var(--text-secondary);
  }

  .message-file-name {
    flex: 1;
    font-size: 14px;
    color: var(--text-primary);
  }

  .message-file-size {
    font-size: 12px;
    color: var(--text-secondary);
  }

  /* Loading animation for bot messages */
  .typing-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: var(--chatgpt-message-padding);
    background-color: var(--message-bot-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    max-width: 200px;
  }

  .typing-dots {
    display: flex;
    gap: 4px;
  }

  .typing-dot {
    width: 8px;
    height: 8px;
    background-color: var(--text-secondary);
    border-radius: 50%;
    animation: typingBounce 1.4s infinite ease-in-out;
  }

  .typing-dot:nth-child(1) { animation-delay: -0.32s; }
  .typing-dot:nth-child(2) { animation-delay: -0.16s; }
  .typing-dot:nth-child(3) { animation-delay: 0s; }

  @keyframes typingBounce {
    0%, 80%, 100% {
      transform: scale(0.8);
      opacity: 0.5;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* Button variants */
  .btn-primary {
    padding: 8px 16px;
    background-color: var(--cta-color);
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .btn-primary:hover {
    background-color: var(--cta-hover);
  }

  .btn-primary:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
  }

  .btn-secondary {
    padding: 8px 16px;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .btn-secondary:hover {
    background-color: var(--border-color);
  }

  .btn-secondary:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
  }

  /* Icon button */
  .icon-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    background: transparent;
    border: none;
    border-radius: 6px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .icon-btn:hover {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
  }

  .icon-btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
  }

  /* Modal overlay */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.2s ease-in-out;
  }

  /* Modal content */
  .modal-content {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: var(--card-shadow);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideIn 0.3s ease-out;
  }

  /* Form styles */
  .form-group {
    margin-bottom: 16px;
  }

  .form-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 8px;
  }

  .form-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background-color: var(--input-bg);
    color: var(--input-text);
    font-size: 14px;
    transition: all 0.2s ease;
  }

  .form-input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(var(--accent-color-rgb), 0.2);
  }

  .form-input::placeholder {
    color: var(--input-placeholder);
  }

  /* Loading spinner */
  .loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid transparent;
    border-top: 2px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}

@layer utilities {
  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Animations */
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes pulseRecording {
    0%, 100% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.1);
      opacity: 0.8;
    }
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    /* Pre-login responsive */
    .pre-login-welcome-message h2 {
      font-size: 24px;
    }

    .pre-login-welcome-message p {
      font-size: 16px;
    }

    .suggestion-chips {
      flex-direction: column;
      align-items: center;
    }

    .pre-login-bottom-input-container {
      bottom: 16px;
      max-width: calc(100% - 32px);
    }

    /* App layout responsive */
    .app-container {
      flex-direction: column;
    }

    .sidebar {
      width: 100%;
      height: auto;
      border-right: none;
      border-bottom: 1px solid var(--border-color);
    }

    .sidebar.collapsed {
      width: 100%;
      height: 60px;
    }

    .sidebar-conversations {
      display: none;
    }

    .sidebar.collapsed .sidebar-conversations {
      display: none;
    }

    .main-content {
      flex: 1;
    }

    .header-content {
      padding: 0 16px;
    }

    .chat-input-container {
      padding: 12px 16px;
    }

    .welcome-message h2 {
      font-size: 24px;
    }

    .welcome-message p {
      font-size: 14px;
    }

    .message-bubble {
      max-width: 85%;
    }

    .user-dropdown-menu {
      width: 250px;
    }
  }

  @media (max-width: 480px) {
    .header-content {
      padding: 0 12px;
    }

    .chat-input-container {
      padding: 8px 12px;
    }

    .pre-login-header {
      padding: 12px 16px;
    }

    .suggestion-chip {
      padding: 10px 16px;
      font-size: 13px;
    }

    .message-bubble {
      max-width: 90%;
      padding: 12px;
    }

    .welcome-message h2 {
      font-size: 20px;
    }

    .user-dropdown-menu {
      width: 220px;
    }
  }

  /* Utility classes for showing/hiding elements based on login state */
  .not-logged-in .logged-in-only {
    display: none !important;
  }

  .logged-in .not-logged-in-only {
    display: none !important;
  }

  /* Scrollbar styling */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: var(--border-color);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: var(--accent-color);
  }
}
